# 教育培训系统项目架构设计

## 1. 项目概览

基于功能需求分析，系统采用微服务架构，分为以下几个独立项目：

### 1.1 项目划分
- **前端项目**：3个独立的前端应用
- **后端服务**：7个微服务
- **AI服务**：3个AI集成服务
- **基础设施**：API网关、数据库、文件存储

### 1.2 技术栈选择
- **前端**：React/Vue.js + TypeScript
- **后端**：Spring Boot + Java 17
- **数据库**：MySQL + Redis
- **消息队列**：RabbitMQ
- **文件存储**：MinIO/阿里云OSS
- **API网关**：Spring Cloud Gateway

## 2. 详细项目结构

### 2.1 前端项目

#### 2.1.1 管理端-公司运营 (admin-company-web)
```
admin-company-web/
├── src/
│   ├── components/          # 通用组件
│   ├── pages/
│   │   ├── school/         # 学校管理
│   │   ├── user/           # 人员管理
│   │   ├── class/          # 班级管理
│   │   ├── course/         # 课程管理
│   │   ├── permission/     # 权限配置
│   │   └── question/       # 题库管理
│   ├── services/           # API服务
│   ├── utils/              # 工具函数
│   └── store/              # 状态管理
├── public/
└── package.json
```

**主要功能模块**：
- 学校数据管理
- 人员数据管理
- 班级数据管理
- 课程数据管理
- 权限配置管理
- 知识数据转题目

#### 2.1.2 管理端-学校老师 (admin-teacher-web)
```
admin-teacher-web/
├── src/
│   ├── components/
│   ├── pages/
│   │   ├── class/          # 班级管理
│   │   ├── student/        # 学生管理
│   │   ├── course/         # 课程管理
│   │   ├── question/       # 题目管理
│   │   ├── training/       # 实训管理
│   │   ├── exam/           # 考试管理
│   │   └── dashboard/      # 数据看板
│   ├── services/
│   ├── utils/
│   └── store/
├── public/
└── package.json
```

**主要功能模块**：
- 班级学生管理
- 课程管理
- 题目创建管理
- 实训试卷创建
- 考试创建管理
- 数据统计分析

#### 2.1.3 用户端-学生 (student-web)
```
student-web/
├── src/
│   ├── components/
│   ├── pages/
│   │   ├── training/       # 实训模块
│   │   ├── exam/           # 考试模块
│   │   ├── dashboard/      # 个人数据
│   │   └── profile/        # 个人信息
│   ├── services/
│   ├── utils/
│   └── store/
├── public/
└── package.json
```

**主要功能模块**：
- 实训练习
- 在线考试
- 成绩查看
- 个人数据统计

### 2.2 后端微服务

#### 2.2.1 用户权限服务 (user-auth-service)
```
user-auth-service/
├── src/main/java/com/edu/auth/
│   ├── controller/         # 控制器层
│   ├── service/           # 业务逻辑层
│   ├── repository/        # 数据访问层
│   ├── entity/            # 实体类
│   ├── dto/               # 数据传输对象
│   ├── config/            # 配置类
│   └── security/          # 安全配置
├── src/main/resources/
│   ├── application.yml
│   └── mapper/            # MyBatis映射文件
└── pom.xml
```

**核心功能**：
- 用户认证授权
- JWT令牌管理
- 角色权限控制
- 登录日志记录

#### 2.2.2 基础数据服务 (base-data-service)
```
base-data-service/
├── src/main/java/com/edu/base/
│   ├── controller/
│   │   ├── SchoolController.java      # 学校管理
│   │   ├── ClassController.java       # 班级管理
│   │   ├── CourseController.java      # 课程管理
│   │   └── UserController.java        # 用户管理
│   ├── service/
│   │   ├── SchoolService.java
│   │   ├── ClassService.java
│   │   ├── CourseService.java
│   │   └── UserService.java
│   ├── repository/
│   ├── entity/
│   │   ├── School.java
│   │   ├── Class.java
│   │   ├── Course.java
│   │   └── User.java
│   └── dto/
├── src/main/resources/
└── pom.xml
```

**核心功能**：
- 学校信息管理
- 班级信息管理
- 课程信息管理
- 用户基础信息管理

#### 2.2.3 题库管理服务 (question-bank-service)
```
question-bank-service/
├── src/main/java/com/edu/question/
│   ├── controller/
│   │   ├── QuestionController.java    # 题目管理
│   │   └── KnowledgeController.java   # 知识点管理
│   ├── service/
│   │   ├── QuestionService.java
│   │   ├── KnowledgeService.java
│   │   └── TTSService.java            # TTS集成
│   ├── repository/
│   ├── entity/
│   │   ├── Question.java
│   │   ├── QuestionType.java
│   │   ├── Knowledge.java
│   │   └── Chapter.java
│   └── dto/
├── src/main/resources/
└── pom.xml
```

**核心功能**：
- 题目创建管理
- 题目类型管理
- 知识点转题目
- TTS语音合成集成
- 题目分类检索

#### 2.2.4 实训管理服务 (training-service)
```
training-service/
├── src/main/java/com/edu/training/
│   ├── controller/
│   │   ├── TrainingController.java    # 实训管理
│   │   └── TrainingPaperController.java # 实训试卷
│   ├── service/
│   │   ├── TrainingService.java
│   │   ├── TrainingPaperService.java
│   │   ├── ASRService.java            # ASR集成
│   │   └── AnalysisService.java       # 成绩分析
│   ├── repository/
│   ├── entity/
│   │   ├── Training.java
│   │   ├── TrainingPaper.java
│   │   ├── TrainingRecord.java
│   │   └── TrainingResult.java
│   └── dto/
├── src/main/resources/
└── pom.xml
```

**核心功能**：
- 实训试卷创建
- 实训过程管理
- 答题记录保存
- ASR语音识别集成
- 实训结果分析

#### 2.2.5 考试管理服务 (exam-service)
```
exam-service/
├── src/main/java/com/edu/exam/
│   ├── controller/
│   │   ├── ExamController.java        # 考试管理
│   │   └── ExamPaperController.java   # 考试试卷
│   ├── service/
│   │   ├── ExamService.java
│   │   ├── ExamPaperService.java
│   │   ├── ExamRecordService.java
│   │   ├── ASRService.java
│   │   └── GradingService.java        # 阅卷服务
│   ├── repository/
│   ├── entity/
│   │   ├── Exam.java
│   │   ├── ExamPaper.java
│   │   ├── ExamRecord.java
│   │   └── ExamResult.java
│   └── dto/
├── src/main/resources/
└── pom.xml
```

**核心功能**：
- 考试创建管理
- 考试过程监控
- 答题记录管理
- 自动阅卷评分
- 成绩统计分析

#### 2.2.6 数据分析服务 (data-analysis-service)
```
data-analysis-service/
├── src/main/java/com/edu/analysis/
│   ├── controller/
│   │   ├── DashboardController.java   # 数据看板
│   │   └── ReportController.java      # 报表生成
│   ├── service/
│   │   ├── DashboardService.java
│   │   ├── ReportService.java
│   │   └── StatisticsService.java     # 统计分析
│   ├── repository/
│   ├── entity/
│   │   ├── Dashboard.java
│   │   └── Report.java
│   └── dto/
├── src/main/resources/
└── pom.xml
```

**核心功能**：
- 实训数据统计
- 考试数据分析
- 学习进度跟踪
- 报表生成导出

#### 2.2.7 通知服务 (notification-service)
```
notification-service/
├── src/main/java/com/edu/notification/
│   ├── controller/
│   │   └── NotificationController.java
│   ├── service/
│   │   ├── NotificationService.java
│   │   ├── EmailService.java
│   │   └── SMSService.java
│   ├── repository/
│   ├── entity/
│   │   └── Notification.java
│   └── dto/
├── src/main/resources/
└── pom.xml
```

**核心功能**：
- 考试开始提醒
- 系统消息推送
- 邮件通知发送
- 短信通知发送

### 2.3 AI服务层

#### 2.3.1 TTS语音合成服务 (tts-service)
```
tts-service/
├── src/main/java/com/edu/tts/
│   ├── controller/
│   │   └── TTSController.java
│   ├── service/
│   │   ├── TTSService.java
│   │   └── AudioFileService.java
│   ├── integration/
│   │   ├── BaiduTTSClient.java        # 百度TTS集成
│   │   └── AliTTSClient.java          # 阿里TTS集成
│   ├── entity/
│   └── dto/
├── src/main/resources/
└── pom.xml
```

**核心功能**：
- 文字转语音合成
- 音频文件管理
- 多厂商TTS集成
- 语音质量优化

#### 2.3.2 ASR语音识别服务 (asr-service)
```
asr-service/
├── src/main/java/com/edu/asr/
│   ├── controller/
│   │   └── ASRController.java
│   ├── service/
│   │   ├── ASRService.java
│   │   └── AudioProcessService.java
│   ├── integration/
│   │   ├── BaiduASRClient.java        # 百度ASR集成
│   │   └── AliASRClient.java          # 阿里ASR集成
│   ├── entity/
│   └── dto/
├── src/main/resources/
└── pom.xml
```

**核心功能**：
- 语音转文字识别
- 音频预处理
- 识别结果优化
- 多厂商ASR集成

#### 2.3.3 成绩分析服务 (score-analysis-service)
```
score-analysis-service/
├── src/main/java/com/edu/analysis/
│   ├── controller/
│   │   └── ScoreAnalysisController.java
│   ├── service/
│   │   ├── ScoreAnalysisService.java
│   │   ├── AIAnalysisService.java
│   │   └── RecommendationService.java
│   ├── integration/
│   │   └── OpenAIClient.java          # 大模型集成
│   ├── entity/
│   └── dto/
├── src/main/resources/
└── pom.xml
```

**核心功能**：
- 成绩智能分析
- 学习建议生成
- 知识点掌握评估
- 个性化推荐

## 3. 数据库设计

### 3.1 用户数据库 (user_db)
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    role ENUM('COMPANY_ADMIN', 'TEACHER', 'STUDENT') NOT NULL,
    school_id BIGINT,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 角色权限表
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL,
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2 业务数据库 (business_db)
```sql
-- 学校表
CREATE TABLE schools (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    address VARCHAR(255),
    contact_person VARCHAR(50),
    contact_phone VARCHAR(20),
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 班级表
CREATE TABLE classes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    school_id BIGINT NOT NULL,
    teacher_id BIGINT,
    grade VARCHAR(20),
    student_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 课程表
CREATE TABLE courses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    school_id BIGINT NOT NULL,
    teacher_id BIGINT,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.3 题库数据库 (question_db)
```sql
-- 题目表
CREATE TABLE questions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    type ENUM('SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'FILL_BLANK', 'ESSAY') NOT NULL,
    course_id BIGINT NOT NULL,
    chapter_id BIGINT,
    difficulty TINYINT DEFAULT 1,
    answer TEXT NOT NULL,
    analysis TEXT,
    audio_url VARCHAR(255),
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 章节表
CREATE TABLE chapters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    course_id BIGINT NOT NULL,
    parent_id BIGINT,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.4 考试数据库 (exam_db)
```sql
-- 实训表
CREATE TABLE trainings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    course_id BIGINT NOT NULL,
    chapter_id BIGINT,
    question_ids JSON NOT NULL,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 考试表
CREATE TABLE exams (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    course_id BIGINT NOT NULL,
    question_ids JSON NOT NULL,
    student_ids JSON NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration INT NOT NULL,
    status ENUM('DRAFT', 'PUBLISHED', 'STARTED', 'ENDED') DEFAULT 'DRAFT',
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 答题记录表
CREATE TABLE answer_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    exam_id BIGINT,
    training_id BIGINT,
    student_id BIGINT NOT NULL,
    question_id BIGINT NOT NULL,
    answer TEXT,
    audio_url VARCHAR(255),
    score DECIMAL(5,2),
    is_correct TINYINT,
    answer_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 4. 服务间依赖关系

### 4.1 核心依赖说明

**基础服务依赖**：
- 所有业务服务都依赖用户权限服务进行身份验证
- 所有业务服务都依赖基础数据服务获取基础信息

**业务服务依赖**：
- 题库管理服务 → TTS服务（题目语音合成）
- 实训管理服务 → 题库管理服务（获取题目）、ASR服务（语音识别）、成绩分析服务（结果分析）
- 考试管理服务 → 题库管理服务（获取题目）、ASR服务（语音识别）、成绩分析服务（结果分析）
- 数据分析服务 → 实训管理服务、考试管理服务（获取数据）
- 通知服务 → 基础数据服务（获取用户信息）

### 4.2 数据流向

**题目创建流程**：
1. 老师在管理端创建题目
2. 题库管理服务保存题目信息
3. 调用TTS服务生成语音文件
4. 返回完整题目信息

**实训流程**：
1. 老师创建实训试卷
2. 学生开始实训答题
3. 语音答案通过ASR服务转换为文字
4. 成绩分析服务分析答题结果
5. 通知服务发送结果通知

**考试流程**：
1. 老师创建考试并选择学生
2. 通知服务发送考试提醒
3. 学生参加考试答题
4. ASR服务处理语音答案
5. 成绩分析服务生成考试报告

## 5. 部署架构

### 5.1 容器化部署
```yaml
# docker-compose.yml 示例
version: '3.8'
services:
  # API网关
  api-gateway:
    image: education-api-gateway:latest
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
    depends_on:
      - redis
      - mysql

  # 用户权限服务
  user-auth-service:
    image: user-auth-service:latest
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis

  # 基础数据服务
  base-data-service:
    image: base-data-service:latest
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
    depends_on:
      - mysql

  # 数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=education_db
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

  # 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 5.2 监控和日志
- **监控**：Prometheus + Grafana
- **日志**：ELK Stack (Elasticsearch + Logstash + Kibana)
- **链路追踪**：Jaeger
- **健康检查**：Spring Boot Actuator

## 6. 开发规范

### 6.1 代码规范
- **Java**：遵循阿里巴巴Java开发手册
- **前端**：ESLint + Prettier
- **数据库**：统一命名规范，使用下划线命名
- **API**：RESTful设计，统一响应格式

### 6.2 版本管理
- **Git Flow**：主分支 + 开发分支 + 功能分支
- **版本号**：语义化版本控制 (Semantic Versioning)
- **代码审查**：Pull Request必须经过审查

### 6.3 测试策略
- **单元测试**：覆盖率不低于80%
- **集成测试**：关键业务流程测试
- **端到端测试**：用户场景测试
- **性能测试**：压力测试和负载测试

## 7. 安全考虑

### 7.1 认证授权
- JWT令牌认证
- RBAC角色权限控制
- API接口权限验证
- 敏感操作二次验证

### 7.2 数据安全
- 数据库连接加密
- 敏感数据脱敏
- 文件上传安全检查
- SQL注入防护

### 7.3 网络安全
- HTTPS通信
- API限流防护
- 跨域请求控制
- 防火墙配置

## 8. 扩展性设计

### 8.1 水平扩展
- 微服务独立部署
- 负载均衡配置
- 数据库读写分离
- 缓存集群部署

### 8.2 功能扩展
- 插件化架构设计
- 配置中心管理
- 多租户支持
- 国际化支持

这个架构设计充分考虑了教育培训系统的业务需求，采用微服务架构确保系统的可扩展性和可维护性，同时集成AI服务提供智能化功能。每个服务职责清晰，便于团队协作开发和后期维护。
