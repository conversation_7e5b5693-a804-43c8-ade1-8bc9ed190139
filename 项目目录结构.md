# 教育培训系统完整目录结构

## 项目根目录结构

```
education-training-system/
├── README.md                           # 项目说明文档
├── docker-compose.yml                  # Docker编排文件
├── .gitignore                         # Git忽略文件
├── docs/                              # 项目文档
│   ├── 项目架构设计.md
│   ├── 项目实施计划.md
│   ├── API接口文档.md
│   └── 数据库设计文档.md
├── scripts/                           # 部署脚本
│   ├── deploy.sh                      # 部署脚本
│   ├── init-db.sql                    # 数据库初始化脚本
│   └── docker/                        # Docker相关脚本
├── frontend/                          # 前端项目目录
│   ├── admin-company-web/             # 公司运营管理端
│   ├── admin-teacher-web/             # 学校老师管理端
│   └── student-web/                   # 学生用户端
├── backend/                           # 后端服务目录
│   ├── user-auth-service/             # 用户权限服务
│   ├── base-data-service/             # 基础数据服务
│   ├── question-bank-service/         # 题库管理服务
│   ├── training-service/              # 实训管理服务
│   ├── exam-service/                  # 考试管理服务
│   ├── data-analysis-service/         # 数据分析服务
│   └── notification-service/          # 通知服务
├── ai-services/                       # AI服务目录
│   ├── tts-service/                   # TTS语音合成服务
│   ├── asr-service/                   # ASR语音识别服务
│   └── score-analysis-service/        # 成绩分析服务
├── infrastructure/                    # 基础设施
│   ├── api-gateway/                   # API网关
│   ├── config-center/                 # 配置中心
│   └── monitoring/                    # 监控配置
└── shared/                           # 共享模块
    ├── common/                       # 通用工具类
    ├── security/                     # 安全相关
    └── database/                     # 数据库相关
```

## 前端项目详细结构

### 1. 公司运营管理端 (admin-company-web)

```
admin-company-web/
├── public/
│   ├── index.html
│   ├── favicon.ico
│   └── manifest.json
├── src/
│   ├── components/                    # 通用组件
│   │   ├── Layout/                    # 布局组件
│   │   │   ├── Header.tsx
│   │   │   ├── Sidebar.tsx
│   │   │   └── index.tsx
│   │   ├── Common/                    # 通用组件
│   │   │   ├── Loading.tsx
│   │   │   ├── ErrorBoundary.tsx
│   │   │   └── ConfirmModal.tsx
│   │   └── Charts/                    # 图表组件
│   │       ├── LineChart.tsx
│   │       ├── BarChart.tsx
│   │       └── PieChart.tsx
│   ├── pages/                         # 页面组件
│   │   ├── Login/                     # 登录页面
│   │   │   ├── index.tsx
│   │   │   └── style.less
│   │   ├── Dashboard/                 # 仪表盘
│   │   │   ├── index.tsx
│   │   │   └── components/
│   │   ├── School/                    # 学校管理
│   │   │   ├── SchoolList.tsx
│   │   │   ├── SchoolForm.tsx
│   │   │   └── SchoolDetail.tsx
│   │   ├── User/                      # 人员管理
│   │   │   ├── UserList.tsx
│   │   │   ├── UserForm.tsx
│   │   │   └── UserImport.tsx
│   │   ├── Class/                     # 班级管理
│   │   │   ├── ClassList.tsx
│   │   │   ├── ClassForm.tsx
│   │   │   └── ClassDetail.tsx
│   │   ├── Course/                    # 课程管理
│   │   │   ├── CourseList.tsx
│   │   │   ├── CourseForm.tsx
│   │   │   └── CourseDetail.tsx
│   │   ├── Permission/                # 权限配置
│   │   │   ├── RoleList.tsx
│   │   │   ├── RoleForm.tsx
│   │   │   └── PermissionTree.tsx
│   │   └── Question/                  # 题库管理
│   │       ├── QuestionList.tsx
│   │       ├── QuestionForm.tsx
│   │       └── KnowledgeImport.tsx
│   ├── services/                      # API服务
│   │   ├── api.ts                     # API配置
│   │   ├── auth.ts                    # 认证相关
│   │   ├── school.ts                  # 学校相关
│   │   ├── user.ts                    # 用户相关
│   │   ├── class.ts                   # 班级相关
│   │   ├── course.ts                  # 课程相关
│   │   ├── permission.ts              # 权限相关
│   │   └── question.ts                # 题库相关
│   ├── store/                         # 状态管理
│   │   ├── index.ts                   # Store配置
│   │   ├── auth.ts                    # 认证状态
│   │   ├── user.ts                    # 用户状态
│   │   └── common.ts                  # 通用状态
│   ├── utils/                         # 工具函数
│   │   ├── request.ts                 # 请求封装
│   │   ├── storage.ts                 # 存储工具
│   │   ├── format.ts                  # 格式化工具
│   │   └── constants.ts               # 常量定义
│   ├── hooks/                         # 自定义Hook
│   │   ├── useAuth.ts                 # 认证Hook
│   │   ├── useTable.ts                # 表格Hook
│   │   └── useForm.ts                 # 表单Hook
│   ├── types/                         # TypeScript类型定义
│   │   ├── api.ts                     # API类型
│   │   ├── user.ts                    # 用户类型
│   │   ├── school.ts                  # 学校类型
│   │   └── common.ts                  # 通用类型
│   ├── styles/                        # 样式文件
│   │   ├── global.less                # 全局样式
│   │   ├── variables.less             # 样式变量
│   │   └── mixins.less                # 样式混入
│   ├── App.tsx                        # 根组件
│   ├── index.tsx                      # 入口文件
│   └── setupTests.ts                  # 测试配置
├── package.json                       # 依赖配置
├── tsconfig.json                      # TypeScript配置
├── craco.config.js                    # 构建配置
└── .env                              # 环境变量
```

### 2. 学校老师管理端 (admin-teacher-web)

```
admin-teacher-web/
├── public/
├── src/
│   ├── components/
│   ├── pages/
│   │   ├── Login/                     # 登录页面
│   │   ├── Dashboard/                 # 仪表盘
│   │   ├── Class/                     # 班级管理
│   │   │   ├── ClassList.tsx
│   │   │   ├── ClassDetail.tsx
│   │   │   └── StudentManage.tsx
│   │   ├── Student/                   # 学生管理
│   │   │   ├── StudentList.tsx
│   │   │   ├── StudentForm.tsx
│   │   │   └── StudentImport.tsx
│   │   ├── Course/                    # 课程管理
│   │   │   ├── CourseList.tsx
│   │   │   ├── CourseForm.tsx
│   │   │   └── ChapterManage.tsx
│   │   ├── Question/                  # 题目管理
│   │   │   ├── QuestionList.tsx
│   │   │   ├── QuestionForm.tsx
│   │   │   ├── QuestionPreview.tsx
│   │   │   └── QuestionImport.tsx
│   │   ├── Training/                  # 实训管理
│   │   │   ├── TrainingList.tsx
│   │   │   ├── TrainingForm.tsx
│   │   │   ├── TrainingDetail.tsx
│   │   │   └── TrainingReport.tsx
│   │   ├── Exam/                      # 考试管理
│   │   │   ├── ExamList.tsx
│   │   │   ├── ExamForm.tsx
│   │   │   ├── ExamMonitor.tsx
│   │   │   └── ExamReport.tsx
│   │   └── Analytics/                 # 数据分析
│   │       ├── TrainingAnalytics.tsx
│   │       ├── ExamAnalytics.tsx
│   │       └── StudentProgress.tsx
│   ├── services/
│   ├── store/
│   ├── utils/
│   ├── types/
│   └── styles/
├── package.json
└── tsconfig.json
```

### 3. 学生用户端 (student-web)

```
student-web/
├── public/
├── src/
│   ├── components/
│   │   ├── Layout/                    # 移动端布局
│   │   ├── AudioPlayer/               # 音频播放器
│   │   ├── AudioRecorder/             # 音频录制器
│   │   └── QuestionCard/              # 题目卡片
│   ├── pages/
│   │   ├── Login/                     # 登录页面
│   │   ├── Home/                      # 首页
│   │   ├── Training/                  # 实训模块
│   │   │   ├── TrainingList.tsx
│   │   │   ├── TrainingDetail.tsx
│   │   │   ├── TrainingExercise.tsx
│   │   │   └── TrainingResult.tsx
│   │   ├── Exam/                      # 考试模块
│   │   │   ├── ExamList.tsx
│   │   │   ├── ExamDetail.tsx
│   │   │   ├── ExamProcess.tsx
│   │   │   └── ExamResult.tsx
│   │   ├── Profile/                   # 个人中心
│   │   │   ├── PersonalInfo.tsx
│   │   │   ├── StudyRecord.tsx
│   │   │   └── Settings.tsx
│   │   └── Analytics/                 # 学习数据
│   │       ├── StudyProgress.tsx
│   │       ├── ScoreAnalysis.tsx
│   │       └── LearningReport.tsx
│   ├── services/
│   ├── store/
│   ├── utils/
│   │   ├── audio.ts                   # 音频处理工具
│   │   ├── recorder.ts                # 录音工具
│   │   └── player.ts                  # 播放工具
│   ├── types/
│   └── styles/
├── package.json
└── tsconfig.json
```

## 后端服务详细结构

### 1. 用户权限服务 (user-auth-service)

```
user-auth-service/
├── src/
│   ├── main/
│   │   ├── java/com/edu/auth/
│   │   │   ├── AuthApplication.java           # 启动类
│   │   │   ├── controller/                    # 控制器层
│   │   │   │   ├── AuthController.java        # 认证控制器
│   │   │   │   ├── UserController.java        # 用户控制器
│   │   │   │   └── RoleController.java        # 角色控制器
│   │   │   ├── service/                       # 服务层
│   │   │   │   ├── AuthService.java           # 认证服务
│   │   │   │   ├── UserService.java           # 用户服务
│   │   │   │   ├── RoleService.java           # 角色服务
│   │   │   │   └── impl/                      # 服务实现
│   │   │   ├── repository/                    # 数据访问层
│   │   │   │   ├── UserRepository.java
│   │   │   │   ├── RoleRepository.java
│   │   │   │   └── PermissionRepository.java
│   │   │   ├── entity/                        # 实体类
│   │   │   │   ├── User.java
│   │   │   │   ├── Role.java
│   │   │   │   ├── Permission.java
│   │   │   │   └── UserRole.java
│   │   │   ├── dto/                           # 数据传输对象
│   │   │   │   ├── LoginRequest.java
│   │   │   │   ├── LoginResponse.java
│   │   │   │   ├── UserDTO.java
│   │   │   │   └── RoleDTO.java
│   │   │   ├── config/                        # 配置类
│   │   │   │   ├── SecurityConfig.java        # 安全配置
│   │   │   │   ├── JwtConfig.java             # JWT配置
│   │   │   │   └── RedisConfig.java           # Redis配置
│   │   │   ├── security/                      # 安全相关
│   │   │   │   ├── JwtTokenProvider.java      # JWT工具类
│   │   │   │   ├── JwtAuthenticationFilter.java # JWT过滤器
│   │   │   │   └── CustomUserDetailsService.java # 用户详情服务
│   │   │   ├── exception/                     # 异常处理
│   │   │   │   ├── GlobalExceptionHandler.java
│   │   │   │   └── AuthException.java
│   │   │   └── util/                          # 工具类
│   │   │       ├── PasswordUtil.java
│   │   │       └── ValidationUtil.java
│   │   └── resources/
│   │       ├── application.yml                # 配置文件
│   │       ├── application-dev.yml            # 开发环境配置
│   │       ├── application-prod.yml           # 生产环境配置
│   │       └── mapper/                        # MyBatis映射文件
│   │           ├── UserMapper.xml
│   │           ├── RoleMapper.xml
│   │           └── PermissionMapper.xml
│   └── test/                                  # 测试代码
│       └── java/com/edu/auth/
│           ├── controller/
│           ├── service/
│           └── repository/
├── Dockerfile                                 # Docker文件
├── pom.xml                                    # Maven配置
└── README.md                                  # 服务说明
```

这个完整的目录结构展示了整个教育培训系统的组织方式，每个项目都有清晰的分层架构和模块划分，便于团队协作开发和后期维护。
