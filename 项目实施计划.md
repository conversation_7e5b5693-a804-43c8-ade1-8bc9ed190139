# 教育培训系统项目实施计划

## 1. 项目创建顺序

### 第一阶段：基础设施搭建（1-2周）

#### 1.1 创建项目骨架
```bash
# 创建根目录
mkdir education-training-system
cd education-training-system

# 创建各个子项目目录
mkdir -p frontend/{admin-company-web,admin-teacher-web,student-web}
mkdir -p backend/{user-auth-service,base-data-service,question-bank-service}
mkdir -p backend/{training-service,exam-service,data-analysis-service,notification-service}
mkdir -p ai-services/{tts-service,asr-service,score-analysis-service}
mkdir -p infrastructure/{api-gateway,config-center}
mkdir -p docs
mkdir -p scripts
```

#### 1.2 基础设施项目
1. **API网关** (api-gateway)
   - Spring Cloud Gateway
   - 路由配置
   - 限流熔断
   - 统一认证

2. **配置中心** (config-center)
   - Spring Cloud Config
   - 环境配置管理
   - 动态配置更新

3. **服务注册中心**
   - Nacos/Eureka
   - 服务发现
   - 健康检查

### 第二阶段：核心服务开发（2-3周）

#### 2.1 用户权限服务 (user-auth-service)
**优先级：最高**
- JWT认证实现
- 角色权限管理
- 用户登录注册
- 权限验证中间件

**关键接口**：
```java
// 用户认证
POST /auth/login
POST /auth/logout
POST /auth/refresh

// 权限管理
GET /auth/permissions
POST /auth/roles
GET /auth/user/info
```

#### 2.2 基础数据服务 (base-data-service)
**优先级：高**
- 学校信息管理
- 班级课程管理
- 用户基础信息
- 组织架构管理

**关键接口**：
```java
// 学校管理
GET /schools
POST /schools
PUT /schools/{id}

// 班级管理
GET /classes
POST /classes
PUT /classes/{id}

// 课程管理
GET /courses
POST /courses
PUT /courses/{id}
```

### 第三阶段：业务服务开发（3-4周）

#### 3.1 题库管理服务 (question-bank-service)
**优先级：高**
- 题目CRUD操作
- 题目分类管理
- 知识点关联
- TTS集成

**关键功能**：
- 题目创建和编辑
- 题目类型管理（单选、多选、填空、问答）
- 章节知识点管理
- 题目搜索和筛选

#### 3.2 实训管理服务 (training-service)
**优先级：中**
- 实训试卷创建
- 实训过程管理
- 答题记录保存
- ASR集成

#### 3.3 考试管理服务 (exam-service)
**优先级：中**
- 考试创建管理
- 考试过程监控
- 自动阅卷评分
- 成绩统计

### 第四阶段：AI服务集成（2-3周）

#### 4.1 TTS语音合成服务
- 百度/阿里云TTS集成
- 音频文件管理
- 语音质量优化

#### 4.2 ASR语音识别服务
- 语音转文字功能
- 音频预处理
- 识别准确率优化

#### 4.3 成绩分析服务
- 智能成绩分析
- 学习建议生成
- 大模型集成

### 第五阶段：前端应用开发（4-5周）

#### 5.1 管理端-公司运营 (admin-company-web)
**技术栈**：React + TypeScript + Ant Design
- 学校管理界面
- 人员管理界面
- 权限配置界面
- 数据统计看板

#### 5.2 管理端-学校老师 (admin-teacher-web)
**技术栈**：React + TypeScript + Ant Design
- 班级学生管理
- 题目创建管理
- 实训考试管理
- 数据分析看板

#### 5.3 用户端-学生 (student-web)
**技术栈**：React + TypeScript + Ant Design Mobile
- 实训练习界面
- 在线考试界面
- 成绩查看界面
- 个人中心

### 第六阶段：系统集成测试（1-2周）

#### 6.1 单元测试
- 服务层单元测试
- 控制器层测试
- 工具类测试

#### 6.2 集成测试
- 服务间接口测试
- 数据库集成测试
- 第三方服务集成测试

#### 6.3 端到端测试
- 完整业务流程测试
- 用户场景测试
- 性能压力测试

## 2. 开发团队分工建议

### 2.1 后端团队（4-5人）
- **架构师**：负责整体架构设计和技术选型
- **核心服务开发**：用户权限服务、基础数据服务
- **业务服务开发**：题库、实训、考试服务
- **AI服务集成**：TTS、ASR、成绩分析服务
- **运维开发**：部署脚本、监控配置

### 2.2 前端团队（3-4人）
- **前端架构师**：负责前端架构设计和组件库
- **管理端开发**：公司运营端、学校老师端
- **用户端开发**：学生端应用
- **UI/UX设计师**：界面设计和用户体验

### 2.3 测试团队（2人）
- **测试工程师**：功能测试、集成测试
- **自动化测试**：自动化测试脚本编写

## 3. 技术选型详细说明

### 3.1 后端技术栈
```xml
<!-- Spring Boot 版本 -->
<spring.boot.version>2.7.0</spring.boot.version>
<spring.cloud.version>2021.0.3</spring.cloud.version>

<!-- 数据库相关 -->
<mysql.version>8.0.29</mysql.version>
<mybatis.plus.version>3.5.2</mybatis.plus.version>
<redis.version>2.7.0</redis.version>

<!-- 工具类 -->
<hutool.version>5.8.5</hutool.version>
<fastjson.version>2.0.12</fastjson.version>
<jwt.version>0.11.5</jwt.version>
```

### 3.2 前端技术栈
```json
{
  "react": "^18.2.0",
  "typescript": "^4.7.4",
  "antd": "^4.21.0",
  "axios": "^0.27.2",
  "react-router-dom": "^6.3.0",
  "redux-toolkit": "^1.8.3",
  "echarts": "^5.3.3"
}
```

### 3.3 部署技术栈
- **容器化**：Docker + Docker Compose
- **编排**：Kubernetes（生产环境）
- **监控**：Prometheus + Grafana
- **日志**：ELK Stack
- **CI/CD**：Jenkins/GitLab CI

## 4. 数据库初始化脚本

### 4.1 创建数据库
```sql
-- 创建数据库
CREATE DATABASE education_user_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE education_business_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE education_question_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE education_exam_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'edu_user'@'%' IDENTIFIED BY 'edu_password_123';
GRANT ALL PRIVILEGES ON education_*.* TO 'edu_user'@'%';
FLUSH PRIVILEGES;
```

## 5. 项目配置模板

### 5.1 application.yml 模板
```yaml
server:
  port: ${SERVER_PORT:8080}

spring:
  application:
    name: ${SERVICE_NAME:education-service}
  
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:edu_user}
    password: ${DB_PASSWORD:edu_password_123}
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DB:0}

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.edu: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

这个实施计划提供了详细的项目创建步骤、开发顺序、团队分工建议和技术配置，可以作为项目启动的指导文档。
